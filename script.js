// 页面加载完成后执行
document.addEventListener('DOMContentLoaded', function() {
    // 初始化页面功能
    initializeNavigation();
    initializeCards();
    initializeTable();
    initializeForms();
});

// 导航功能
function initializeNavigation() {
    const navItems = document.querySelectorAll('.nav-item');
    
    navItems.forEach(item => {
        item.addEventListener('click', function() {
            // 移除所有活动状态
            navItems.forEach(nav => nav.classList.remove('active'));
            // 添加当前活动状态
            this.classList.add('active');
        });
    });
}

// 卡片展开/收起功能
function initializeCards() {
    const cardHeaders = document.querySelectorAll('.card-header');
    
    cardHeaders.forEach(header => {
        header.addEventListener('click', function() {
            const card = this.parentElement;
            const content = card.querySelector('.card-content');
            const expandIcon = this.querySelector('.expand-icon');
            
            if (content.style.display === 'none') {
                content.style.display = 'block';
                expandIcon.textContent = '▼';
            } else {
                content.style.display = 'none';
                expandIcon.textContent = '▶';
            }
        });
    });
}

// 表格功能
function initializeTable() {
    // 添加删除按钮功能
    const deleteButtons = document.querySelectorAll('.data-table td:last-child');
    
    deleteButtons.forEach(button => {
        if (button.textContent.trim() === '删除') {
            button.style.cursor = 'pointer';
            button.style.color = '#e74c3c';
            
            button.addEventListener('click', function() {
                if (confirm('确定要删除这条记录吗？')) {
                    const row = this.parentElement;
                    row.remove();
                    updateTableNumbers();
                }
            });
        }
    });
    
    // 添加表格行悬停效果
    const tableRows = document.querySelectorAll('.data-table tbody tr');
    tableRows.forEach(row => {
        row.addEventListener('mouseenter', function() {
            this.style.backgroundColor = '#f8f9fa';
        });
        
        row.addEventListener('mouseleave', function() {
            this.style.backgroundColor = '';
        });
    });
}

// 更新表格序号
function updateTableNumbers() {
    const rows = document.querySelectorAll('.data-table tbody tr');
    rows.forEach((row, index) => {
        const lastCell = row.querySelector('td:nth-last-child(2)');
        if (lastCell) {
            const numbers = ['一', '二', '三', '四', '五', '六', '七', '八', '九', '十'];
            lastCell.textContent = numbers[index] || (index + 1).toString();
        }
    });
}

// 表单功能
function initializeForms() {
    // 添加表单验证
    const formInputs = document.querySelectorAll('.form-input');
    
    formInputs.forEach(input => {
        input.addEventListener('blur', function() {
            validateInput(this);
        });
        
        input.addEventListener('input', function() {
            // 清除错误状态
            this.classList.remove('error');
        });
    });
}

// 输入验证
function validateInput(input) {
    const value = input.value.trim();
    
    if (value === '') {
        input.classList.add('error');
        input.style.borderColor = '#e74c3c';
        return false;
    } else {
        input.classList.remove('error');
        input.style.borderColor = '#ddd';
        return true;
    }
}

// 添加新的餐补记录
function addMealRecord() {
    const tableBody = document.querySelector('.data-table tbody');
    const newRow = document.createElement('tr');
    const currentDate = new Date();
    const year = currentDate.getFullYear();
    const month = currentDate.getMonth() + 1;
    const daysInMonth = new Date(year, month, 0).getDate();

    newRow.innerHTML = `
        <td><a href="#" class="link">${year}年${month}月</a></td>
        <td>${year}-${String(month).padStart(2, '0')}-01</td>
        <td>${year}-${String(month).padStart(2, '0')}-${daysInMonth}</td>
        <td>杭州总部</td>
        <td>标准餐补</td>
        <td>${daysInMonth}</td>
        <td>1500.00</td>
        <td>全月餐补</td>
        <td>删除</td>
    `;

    tableBody.appendChild(newRow);
    initializeTable(); // 重新初始化表格功能
}

// 同步按钮功能
document.addEventListener('DOMContentLoaded', function() {
    const syncButton = document.querySelector('.btn-primary');
    if (syncButton) {
        syncButton.addEventListener('click', function() {
            this.textContent = '同步中...';
            this.disabled = true;
            
            // 模拟同步过程
            setTimeout(() => {
                this.textContent = '同步';
                this.disabled = false;
                alert('数据同步完成！');
            }, 2000);
        });
    }
});

// 工具函数：格式化日期
function formatDate(date) {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
}

// 工具函数：计算餐补总额
function calculateTotalAmount() {
    const rows = document.querySelectorAll('.data-table tbody tr');
    let total = 0;
    
    rows.forEach(row => {
        const daysCell = row.querySelector('td:nth-child(7)');
        if (daysCell) {
            const days = parseInt(daysCell.textContent) || 0;
            total += days * 50; // 假设每天50元标准
        }
    });
    
    return total;
}

// 导出功能（可选）
function exportToExcel() {
    alert('导出功能开发中...');
}

// 打印功能（可选）
function printPage() {
    window.print();
}
