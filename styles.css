* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Microsoft YaHei', Arial, sans-serif;
    background-color: #f5f5f5;
    color: #333;
}

.container {
    display: flex;
    height: 100vh;
}

/* 左侧导航栏 */
.sidebar {
    width: 200px;
    background-color: #2c3e50;
    color: white;
    overflow-y: auto;
}

.logo {
    display: flex;
    align-items: center;
    padding: 15px;
    background-color: #34495e;
    border-bottom: 1px solid #3d566e;
}

.logo img {
    width: 24px;
    height: 24px;
    margin-right: 8px;
}

.logo span {
    font-size: 16px;
    font-weight: bold;
    color: #6666ff;
}

.nav-menu {
    padding: 10px 0;
}

.nav-item {
    display: flex;
    align-items: center;
    padding: 12px 20px;
    cursor: pointer;
    transition: background-color 0.3s;
    font-size: 14px;
}

.nav-item:hover {
    background-color: #34495e;
}

.nav-item.active {
    background-color: #3498db;
}

.nav-icon {
    margin-right: 10px;
    width: 16px;
}

/* 主内容区域 */
.main-content {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
}

/* 工具栏 */
.toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding: 10px 0;
}

.btn-primary {
    background-color: #3498db;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
}

.btn-primary:hover {
    background-color: #2980b9;
}

.toolbar-right {
    display: flex;
    gap: 20px;
}

.user-info {
    font-size: 14px;
    color: #666;
}

/* 卡片样式 */
.card {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    margin-bottom: 20px;
    overflow: hidden;
}

.card-header {
    background-color: #ecf0f1;
    padding: 12px 20px;
    border-bottom: 1px solid #ddd;
    display: flex;
    align-items: center;
    font-weight: bold;
}

.expand-icon {
    margin-right: 8px;
    color: #3498db;
}

.card-title {
    margin-right: 10px;
}

.highlight-text {
    color: #e74c3c;
    font-size: 12px;
}

.card-content {
    padding: 20px;
}

/* 表单样式 */
.form-row {
    display: flex;
    gap: 20px;
    margin-bottom: 15px;
    align-items: center;
}

.form-group {
    display: flex;
    align-items: center;
    gap: 8px;
    min-width: 200px;
}

.form-group.full-width {
    flex: 1;
}

.form-group label {
    font-weight: bold;
    color: #555;
    min-width: 80px;
}

.form-input {
    border: 1px solid #ddd;
    padding: 6px 10px;
    border-radius: 4px;
    flex: 1;
}

/* 表格样式 */
.table-container {
    overflow-x: auto;
}

.data-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 12px;
}

.data-table th,
.data-table td {
    border: 1px solid #ddd;
    padding: 8px;
    text-align: left;
}

.data-table th {
    background-color: #f8f9fa;
    font-weight: bold;
    white-space: nowrap;
}

.data-table td {
    white-space: nowrap;
}

.link {
    color: #3498db;
    text-decoration: none;
}

.link:hover {
    text-decoration: underline;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container {
        flex-direction: column;
    }
    
    .sidebar {
        width: 100%;
        height: auto;
    }
    
    .form-row {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .form-group {
        width: 100%;
        min-width: auto;
    }
}
